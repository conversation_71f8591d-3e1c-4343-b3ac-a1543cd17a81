 /**
 * @file Basic_Usage.ino
 * @brief IMU融合库基本使用示例
 * <AUTHOR> IMU Fusion Project
 * @version 1.0
 * @date 2025-01-02
 * 
 * 这个示例展示了如何使用IMU融合库进行基本的姿态估计
 * 支持自动传感器检测和简单的API调用
 */

#include <IMU_Fusion_Library.h>

// 创建IMU融合库实例
IMU_Fusion_Library imu_fusion;

// 配置参数
#define SAMPLE_RATE_HZ      100     // 采样率 100Hz
#define UPDATE_INTERVAL_MS  10      // 更新间隔 10ms
#define PRINT_INTERVAL_MS   100     // 打印间隔 100ms (10Hz输出)

// I2C引脚定义 (ESP32-C3)
#define SDA_PIN 8
#define SCL_PIN 9

// 时间控制变量
unsigned long last_update_time = 0;
unsigned long last_print_time = 0;

void setup() {
    // 初始化串口
    Serial.begin(115200);
    while (!Serial) {
        delay(10);
    }
    
    Serial.println("========================================");
    Serial.println("IMU融合库基本使用示例");
    Serial.println("========================================");
    
    // 创建传感器和融合算法配置
    IMU_Config sensor_config = createDefaultSensorConfig(16, 2000, 125);
    Fusion_Config fusion_config = createDefaultFusionConfig(SAMPLE_RATE_HZ);
    
    // 自动检测并初始化IMU传感器
    Serial.println("正在检测IMU传感器...");
    IMU_Status status = imu_fusion.autoDetectAndInit(SDA_PIN, SCL_PIN, 400000);
    
    if (status != IMU_Status::OK) {
        Serial.println("错误: 未检测到支持的IMU传感器!");
        Serial.println("请检查:");
        Serial.println("1. 传感器连接是否正确");
        Serial.println("2. I2C引脚配置 (SDA=GPIO8, SCL=GPIO9)");
        Serial.println("3. 传感器供电是否正常");
        while (1) {
            delay(1000);
        }
    }
    
    // 初始化融合库
    Serial.println("正在初始化融合算法...");
    status = imu_fusion.begin(sensor_config, fusion_config);
    
    if (status != IMU_Status::OK) {
        Serial.println("错误: 融合库初始化失败!");
        Serial.printf("错误信息: %s\n", imu_fusion.getLastError());
        while (1) {
            delay(1000);
        }
    }
    
    // 显示传感器信息
    Serial.println("传感器信息:");
    Serial.println(imu_fusion.getSensorInfo());
    Serial.println();
    
    // 设置更新频率
    imu_fusion.setUpdateFrequency(SAMPLE_RATE_HZ);
    
    Serial.println("系统准备就绪，开始数据采集...");
    Serial.println("输出格式: w,x,y,z (四元数)");
    Serial.println("========================================");
    
    last_update_time = millis();
    last_print_time = millis();
}

void loop() {
    unsigned long current_time = millis();
    
    // 控制更新频率
    if (current_time - last_update_time >= UPDATE_INTERVAL_MS) {
        last_update_time = current_time;
        
        // 更新IMU数据并执行融合
        IMU_Status status = imu_fusion.update();
        
        if (status != IMU_Status::OK) {
            Serial.printf("警告: 数据更新失败 - %s\n", imu_fusion.getLastError());
        }
    }
    
    // 控制输出频率
    if (current_time - last_print_time >= PRINT_INTERVAL_MS) {
        last_print_time = current_time;
        
        // 输出四元数 (与原有格式兼容)
        imu_fusion.printQuaternion(4);
    }
    
    // 短暂延时，避免占用过多CPU
    delay(1);
}
